'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Bot,
  Brain,
  Settings,
  Shield,
  LogOut,
  BookOpen
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';
import SearchBox from '@/components/search/SearchBox';
import SearchHistory from '@/components/search/SearchHistory';
import { saveSearchQuery } from '@/lib/search-history';

export default function HomePage() {
  const [user, setUser] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, []);





  const handleSearch = async (query: string) => {
    setIsLoading(true);
    try {
      // Save search query to history (async, don't wait for completion)
      saveSearchQuery(query, user?.id).catch(error => {
        console.error('Error saving search query to history:', error);
      });

      // Navigate to search results page with query parameter
      router.push(`/search?q=${encodeURIComponent(query)}`);
    } catch (error) {
      console.error('Error navigating to search:', error);
      toast.error('Failed to perform search. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };



  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };




  return (
    <div className="min-h-screen bg-[#fffefa] flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#FF6800] rounded-lg">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">Tanya LPDP</h1>
                <p className="text-slate-600 text-sm flex items-center gap-2">
                  <Brain className="w-3 h-3" />
                  AI Search dengan sumber terkurasi
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user ? (
                <>
                  <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs">
                    <Shield className="w-3 h-3" />
                    Authenticated
                  </Badge>
                  <Link href="/admin">
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4 mr-2" />
                      Admin Panel
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" onClick={handleSignOut}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Hero Section */}
        <div className="container mx-auto px-4 py-12 max-w-4xl text-center">
          <div className="mb-8">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
              Tanya LPDP
            </h2>
            <p className="text-lg md:text-xl text-slate-600 mb-2">
              AI Chatbot dengan sumber terkurasi
            </p>
            <p className="text-sm text-slate-500">
              Cari informasi tentang LPDP dari sumber-sumber terpercaya
            </p>
          </div>

          {/* Search Form */}
          <div className="mb-8">
            <SearchBox
              onSearch={handleSearch}
              placeholder="Masukkan pertanyaanmu..."
              isLoading={isLoading}
              size="lg"
              className="max-w-2xl mx-auto"
            />
          </div>

          {/* Search History and Suggestions */}
          <SearchHistory onSuggestionClick={handleSearch} />

          {/* Knowledge Base Info */}
          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <Card className="border-slate-200 shadow-sm">
              <CardContent className="p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="p-3 bg-blue-100 rounded-full">
                    <BookOpen className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <h4 className="font-semibold text-slate-800 mb-2">Situs Resmi LPDP</h4>
                <p className="text-sm text-slate-600 mb-4">
                  Informasi resmi dari website LPDP
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSearch('situs resmi LPDP')}
                  className="rounded-full"
                >
                  Jelajahi
                </Button>
              </CardContent>
            </Card>

            <Card className="border-slate-200 shadow-sm">
              <CardContent className="p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="p-3 bg-green-100 rounded-full">
                    <Brain className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <h4 className="font-semibold text-slate-800 mb-2">Situs Resmi Mata Garuda</h4>
                <p className="text-sm text-slate-600 mb-4">
                  Portal beasiswa dan informasi pendidikan
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSearch('mata garuda')}
                  className="rounded-full"
                >
                  Jelajahi
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6 max-w-6xl">
          <div className="text-center text-sm text-slate-500">
            <p className="mb-2">
              Dikembangkan oleh <strong>Guntur</strong> & <strong>Hatta</strong>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
